[gd_scene load_steps=8 format=3 uid="uid://bqxvn8qkqxqxq"]

[ext_resource type="Script" uid="uid://benjyd1bxcie" path="res://scripts/DialogueSystem.gd" id="1_1x1x1"]
[ext_resource type="Theme" uid="uid://dor07h2n53sgh" path="res://themes/DarkTemplarTheme.tres" id="2_theme"]
[ext_resource type="Texture2D" uid="uid://tfrk1jdt7gio" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Middle Message/Panel.png" id="3_dialogue_panel"]
[ext_resource type="Texture2D" uid="uid://dv7iqf557av33" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Middle Message/Button/Button normal.png" id="4_button_normal"]
[ext_resource type="Texture2D" uid="uid://8sprbwyw5ri8" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Middle Message/Button/Button hover.png" id="5_button_hover"]
[ext_resource type="Texture2D" uid="uid://hp3v30iav43t" path="res://assets/Avatary/Rozpravac.png" id="6_narrator_portrait"]

[sub_resource type="LabelSettings" id="LabelSettings_speaker"]
font_size = 26
font_color = Color(1, 0.9, 0.7, 1)
outline_size = 2
outline_color = Color(0.2, 0.1, 0.05, 1)
shadow_color = Color(0, 0, 0, 0.8)
shadow_offset = Vector2(2, 2)

[node name="DialogueSystem" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme = ExtResource("2_theme")
script = ExtResource("1_1x1x1")

[node name="DialoguePanel" type="NinePatchRect" parent="."]
layout_mode = 1
anchors_preset = 12
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 50.0
offset_top = -616.0
offset_right = -50.0
offset_bottom = -40.0
grow_horizontal = 2
grow_vertical = 0
texture = ExtResource("3_dialogue_panel")
patch_margin_left = 30
patch_margin_top = 30
patch_margin_right = 30
patch_margin_bottom = 30

[node name="MainContainer" type="VBoxContainer" parent="DialoguePanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 30.0
offset_top = 30.0
offset_right = -30.0
offset_bottom = -30.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/separation = 18

[node name="HeaderContainer" type="HBoxContainer" parent="DialoguePanel/MainContainer"]
layout_mode = 2
size_flags_vertical = 0

[node name="CharacterPortrait" type="TextureRect" parent="DialoguePanel/MainContainer/HeaderContainer"]
visible = false
custom_minimum_size = Vector2(80, 80)
layout_mode = 2
size_flags_horizontal = 0
texture = ExtResource("6_narrator_portrait")
expand_mode = 1
stretch_mode = 5

[node name="SpeakerLabel" type="Label" parent="DialoguePanel/MainContainer/HeaderContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "Rozprávač"
label_settings = SubResource("LabelSettings_speaker")
vertical_alignment = 1

[node name="TextContainer" type="Control" parent="DialoguePanel/MainContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="TextLabel" type="RichTextLabel" parent="DialoguePanel/MainContainer/TextContainer"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 15.0
offset_right = -20.0
offset_bottom = -15.0
grow_horizontal = 2
grow_vertical = 2
theme_override_font_sizes/normal_font_size = 22
theme_override_font_sizes/bold_font_size = 24
theme_override_font_sizes/italics_font_size = 22
theme_override_constants/line_separation = 9
bbcode_enabled = true
text = "Text dialógu sa zobrazí tu..."
scroll_active = false
autowrap_mode = 3

[node name="ButtonContainer" type="HBoxContainer" parent="DialoguePanel/MainContainer"]
layout_mode = 2
size_flags_horizontal = 8

[node name="MainMenuButton" type="TextureButton" parent="DialoguePanel/MainContainer/ButtonContainer"]
custom_minimum_size = Vector2(120, 40)
layout_mode = 2
texture_normal = ExtResource("4_button_normal")
texture_pressed = ExtResource("5_button_hover")
texture_hover = ExtResource("5_button_hover")
stretch_mode = 1

[node name="MainMenuLabel" type="Label" parent="DialoguePanel/MainContainer/ButtonContainer/MainMenuButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
text = "Menu"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ContinueButton" type="TextureButton" parent="DialoguePanel/MainContainer/ButtonContainer"]
custom_minimum_size = Vector2(120, 40)
layout_mode = 2
texture_normal = ExtResource("4_button_normal")
texture_pressed = ExtResource("5_button_hover")
texture_hover = ExtResource("5_button_hover")
stretch_mode = 1

[node name="ContinueLabel" type="Label" parent="DialoguePanel/MainContainer/ButtonContainer/ContinueButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
text = "Ďalej"
horizontal_alignment = 1
vertical_alignment = 1

[node name="NarratorAudioPlayer" type="AudioStreamPlayer" parent="."]
volume_db = -7.0

[node name="LightningVideo" type="VideoStreamPlayer" parent="."]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
expand = true
