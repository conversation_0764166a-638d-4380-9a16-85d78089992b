[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://lbnamsqv1jf8"
path="res://.godot/imported/Icon-87.png-8c23d11fe3f7ed0ec6f39a1bf1373eaa.ctex"
metadata={
"vram_texture": false
}

[deps]

source_file="res://builds/prekliatededicstvo/Images.xcassets/AppIcon.appiconset/Icon-87.png"
dest_files=["res://.godot/imported/Icon-87.png-8c23d11fe3f7ed0ec6f39a1bf1373eaa.ctex"]

[params]

compress/mode=0
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=false
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=1
